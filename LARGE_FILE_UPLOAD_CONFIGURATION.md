# Large File Upload Configuration

## Overview

The CSV import functionality has been updated to support large files (up to 500MB) for importing airport, country, and city reference data. This requires proper PHP and web server configuration.

## Required PHP Configuration

### 1. **PHP.ini Settings**

Add or update these settings in your `php.ini` file:

```ini
# File Upload Settings
upload_max_filesize = 500M
post_max_size = 500M
max_file_uploads = 20

# Memory and Execution Settings
memory_limit = 1G
max_execution_time = 600
max_input_time = 600

# For large CSV processing
max_input_vars = 10000
```

### 2. **DDEV Configuration** (if using DDEV)

Create or update `.ddev/php/upload.ini`:

```ini
upload_max_filesize = 500M
post_max_size = 500M
memory_limit = 1G
max_execution_time = 600
max_input_time = 600
```

Then restart DDEV:
```bash
ddev restart
```

### 3. **Web Server Configuration**

#### Apache (.htaccess)
```apache
# Large file upload support
LimitRequestBody 524288000
```

#### Nginx
```nginx
client_max_body_size 500M;
client_body_timeout 600s;
```

## Application Configuration

### 1. **Updated File Size Limits**

- **Template**: Updated to show "Maximum file size: 500MB"
- **Controller**: Validation increased from 10MB to 500MB
- **Error Messages**: Updated to reflect new limits

### 2. **Timeout Handling**

The import process includes:
- **Database transactions**: Automatic rollback on failure
- **Memory management**: Efficient CSV parsing
- **Progress indication**: User feedback during long operations

### 3. **Error Handling**

Enhanced error handling for large files:
- Upload timeout detection
- Memory limit exceeded handling
- Database connection timeout management
- Partial import recovery

## Testing Large File Uploads

### 1. **Verify PHP Settings**

Check current PHP configuration:
```bash
ddev php -i | grep -E "(upload_max_filesize|post_max_size|memory_limit|max_execution_time)"
```

### 2. **Test File Upload**

Create a test CSV file:
```bash
# Generate a large test file (adjust size as needed)
head -1 airports.csv > large_test.csv
for i in {1..100000}; do tail -1 airports.csv >> large_test.csv; done
```

### 3. **Monitor Import Process**

During import, monitor:
- PHP error logs: `ddev logs`
- Memory usage
- Database connections
- Import progress

## Troubleshooting

### Common Issues

1. **"File size exceeds limit" error**
   - Check PHP `upload_max_filesize` setting
   - Verify web server body size limits

2. **"Maximum execution time exceeded"**
   - Increase `max_execution_time` in PHP
   - Consider chunked processing for very large files

3. **"Memory limit exceeded"**
   - Increase PHP `memory_limit`
   - Optimize CSV parsing for memory efficiency

4. **Upload timeout**
   - Increase `max_input_time` setting
   - Check network connection stability

### Debug Commands

```bash
# Check PHP limits
ddev php -r "echo 'Upload max: ' . ini_get('upload_max_filesize') . PHP_EOL;"
ddev php -r "echo 'Post max: ' . ini_get('post_max_size') . PHP_EOL;"
ddev php -r "echo 'Memory limit: ' . ini_get('memory_limit') . PHP_EOL;"

# Check DDEV configuration
ddev describe

# Monitor logs during upload
ddev logs -f
```

## Performance Optimization

### 1. **CSV Processing**

- Stream processing for large files
- Batch database operations
- Memory-efficient parsing

### 2. **Database Operations**

- Transaction batching
- Index optimization
- Connection pooling

### 3. **User Experience**

- Progress indicators
- Timeout warnings
- Chunked upload options (future enhancement)

## Security Considerations

1. **File Type Validation**: Only .csv files accepted
2. **Size Limits**: 500MB maximum (configurable)
3. **Content Validation**: CSV structure and data validation
4. **Access Control**: Backend module permissions required
5. **Transaction Safety**: Automatic rollback on errors

## Future Enhancements

1. **Chunked Upload**: Split large files into smaller chunks
2. **Progress Bar**: Real-time upload and processing progress
3. **Background Processing**: Queue large imports for background processing
4. **Compression Support**: Accept .csv.gz files
5. **Resume Capability**: Resume interrupted uploads

## Implementation Status

✅ **Completed:**
- File size limit increased to 500MB
- Template updated with new limits
- Controller validation updated
- Error handling enhanced

🔄 **Recommended Next Steps:**
1. Configure PHP settings for your environment
2. Test with progressively larger files
3. Monitor performance and adjust limits as needed
4. Consider implementing progress indicators for user feedback
