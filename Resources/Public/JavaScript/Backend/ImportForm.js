/**
 * Import Form JavaScript for Landing Pages Extension
 */
document.addEventListener('DOMContentLoaded', function() {
    const submitBtn = document.getElementById('submitBtn');
    const csvFile = document.getElementById('csvFile');
    const importForm = document.getElementById('importForm');
    
    if (!submitBtn || !csvFile || !importForm) {
        console.error('Import form elements not found');
        return;
    }
    
    function updateSubmitButton() {
        console.log('Updating submit button, files:', csvFile.files.length);
        submitBtn.disabled = !(csvFile.files.length > 0);
        console.log('Submit button disabled:', submitBtn.disabled);
    }
    
    // Initial check
    updateSubmitButton();
    
    csvFile.addEventListener('change', function() {
        console.log('File input changed');
        updateSubmitButton();
    });
    
    // Enhanced confirmation dialog before submit
    importForm.addEventListener('submit', function(e) {
        e.preventDefault();

        // Get table title from data attribute or global variable
        const tableTitle = importForm.dataset.tableTitle || 'data';
        const confirmMessage =
            'IMPORTANT WARNING\n\n' +
            'This operation will COMPLETELY REPLACE all existing ' + tableTitle + ' data and translations.\n\n' +
            'This action CANNOT be undone.\n\n' +
            'Are you sure you want to continue?';

        if (confirm(confirmMessage)) {
            console.log('User confirmed import, submitting form...');

            // Add hidden input to confirm the operation
            const confirmInput = document.createElement('input');
            confirmInput.type = 'hidden';
            confirmInput.name = 'confirmReplace';
            confirmInput.value = '1';
            this.appendChild(confirmInput);

            console.log('Form action:', this.action);
            console.log('Form target:', this.target);

            // Submit the form normally
            this.submit();
        }
    });
});
