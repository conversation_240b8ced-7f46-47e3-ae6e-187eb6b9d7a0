<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
      xmlns:core="http://typo3.org/ns/TYPO3/CMS/Core/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Module" />

<f:section name="Content">
    <div class="landing-pages-module">
        <!-- Header -->
        <div class="panel panel-default">
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-8">
                        <h3>
                            <core:icon identifier="{tableConfig.icon}" size="small" />
                            Import {tableConfig.title} Data
                        </h3>
                        <p class="text-muted">
                            Upload a CSV file to import {tableConfig.title} reference data. This will replace all existing data in the table.
                        </p>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="module-icon">
                            <span>📁</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import Form -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">Upload CSV File</h4>
            </div>
            <div class="panel-body">
                <f:if condition="{errorMessage}">
                    <div class="alert alert-danger">
                        <strong>Error:</strong> {errorMessage}
                    </div>
                </f:if>

                <f:if condition="{successMessage}">
                    <div class="alert alert-success">
                        <strong>Success:</strong> {successMessage}
                    </div>
                </f:if>

                <form action="{be:uri.buildUriFromRoute('web_landingpages.import')}" method="post" enctype="multipart/form-data" id="importForm">
                    <input type="hidden" name="table" value="{table}" />
                    <input type="hidden" name="returnUrl" value="{returnUrl}" />

                    <div class="form-group">
                        <label for="csvFile">CSV File:</label>
                        <input type="file" class="form-control" id="csvFile" name="csvFile" accept=".csv" required>
                        <small class="form-text text-muted">
                            Select a CSV file containing {tableConfig.title} data. Maximum file size: 10MB.
                        </small>
                    </div>

                    <div class="alert alert-info">
                        <h5><strong>CSV Format Requirements:</strong></h5>
                        <ul>
                            <li>First row must contain column headers</li>
                            <li>Required columns: {tableConfig.requiredColumns -> f:format.raw()}</li>
                            <li>Translation columns: Add language code to column name (e.g., "name_de", "name_fr")</li>
                            <li>Encoding: UTF-8</li>
                            <li>Delimiter: Comma (,)</li>
                        </ul>
                        
                        <h6><strong>Example for {tableConfig.title}:</strong></h6>
                        <pre class="csv-example">{tableConfig.csvExample -> f:format.raw()}</pre>
                    </div>

                    <div class="alert alert-warning">
                        <h5><strong>⚠️ Important Warning:</strong></h5>
                        <p>This operation will <strong>completely replace</strong> all existing {tableConfig.title} data and translations. This action cannot be undone.</p>
                    </div>

                    <div class="form-group">
                        <button type="submit" class="btn btn-primary" id="submitBtn" disabled>
                            <core:icon identifier="actions-download" size="small" />
                            Import Data
                        </button>
                        <a href="{returnUrl}" class="btn btn-default">
                            <core:icon identifier="actions-close" size="small" />
                            Cancel
                        </a>
                    </div>
                </form>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">Process Information</h4>
            </div>
            <div class="panel-body">
                <h5>What happens during import:</h5>
                <ol>
                    <li><strong>Validation:</strong> CSV format and required columns are checked</li>
                    <li><strong>Backup:</strong> Current data is temporarily backed up</li>
                    <li><strong>Clear Tables:</strong> Existing data and translations are removed</li>
                    <li><strong>Import:</strong> New data is inserted from CSV</li>
                    <li><strong>Verification:</strong> Data integrity is verified</li>
                </ol>

                <h5>Supported Languages:</h5>
                <p>Add language-specific columns using language codes: <code>en</code>, <code>de</code>, <code>fr</code>, <code>es</code>, <code>it</code>, <code>bg</code>, etc.</p>
                
                <h5>Error Handling:</h5>
                <p>If any errors occur during import, the operation will be rolled back and the original data will be restored.</p>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const submitBtn = document.getElementById('submitBtn');
            const csvFile = document.getElementById('csvFile');

            function updateSubmitButton() {
                console.log('Updating submit button, files:', csvFile.files.length);
                submitBtn.disabled = !(csvFile.files.length > 0);
                console.log('Submit button disabled:', submitBtn.disabled);
            }

            // Initial check
            updateSubmitButton();

            csvFile.addEventListener('change', function() {
                console.log('File input changed');
                updateSubmitButton();
            });

            // Enhanced confirmation dialog before submit
            document.getElementById('importForm').addEventListener('submit', function(e) {
                e.preventDefault();

                const tableTitle = '{tableConfig.title}';
                const confirmMessage =
                    'IMPORTANT WARNING\n\n' +
                    'This operation will COMPLETELY REPLACE all existing ' + tableTitle + ' data and translations.\n\n' +
                    'This action CANNOT be undone.\n\n' +
                    'Are you sure you want to continue?';

                if (confirm(confirmMessage)) {
                    // Add hidden input to confirm the operation
                    const confirmInput = document.createElement('input');
                    confirmInput.type = 'hidden';
                    confirmInput.name = 'confirmReplace';
                    confirmInput.value = '1';
                    this.appendChild(confirmInput);

                    // Submit the form
                    this.submit();
                }
            });
        });
    </script>

    <style>
        .csv-example {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 8px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre;
        }
        
        .module-icon {
            font-size: 3em;
            opacity: 0.3;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .alert h5, .alert h6 {
            margin-top: 0;
        }
        
        .alert ul {
            margin-bottom: 0;
        }
        
        .checkbox {
            margin-top: 10px;
        }
    </style>
</f:section>

</html>
