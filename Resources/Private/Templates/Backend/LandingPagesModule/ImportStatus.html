<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
      xmlns:core="http://typo3.org/ns/TYPO3/CMS/Core/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Module" />

<f:section name="Content">
    <div class="landing-pages-module">
        <!-- Error Details Panel (Full Width, Top) -->
        <f:if condition="{result.success} == 0">
            <div class="panel panel-danger">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <core:icon identifier="actions-exclamation-triangle" size="small" />
                        Import Failed - {result.analysis.error_type}
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="alert alert-danger">
                        <strong>Error Message:</strong><br>
                        {result.analysis.error_message}
                    </div>

                    <f:if condition="{result.analysis.header_columns}">
                        <div class="row">
                            <div class="col-md-6">
                                <h5>Detected Columns ({result.analysis.header_columns -> f:count()}):</h5>
                                <div class="well well-sm" style="max-height: 150px; overflow-y: auto;">
                                    <f:for each="{result.analysis.header_columns}" as="column" iteration="iteration">
                                        <code>{column}</code><f:if condition="{iteration.isLast}"><f:else>, </f:else></f:if>
                                    </f:for>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <f:if condition="{result.analysis.debug_info}">
                                    <h5>Debug Information:</h5>
                                    <dl class="dl-horizontal">
                                        <dt>Request Method:</dt>
                                        <dd>{result.analysis.debug_info.request_method}</dd>
                                        <dt>Content Type:</dt>
                                        <dd>{result.analysis.debug_info.content_type}</dd>
                                        <dt>Files Uploaded:</dt>
                                        <dd>{result.analysis.debug_info.uploaded_files_count}</dd>
                                    </dl>
                                </f:if>
                            </div>
                        </div>
                    </f:if>
                </div>
            </div>
        </f:if>

        <!-- File Upload & CSV Analysis (Two Panels Side by Side) -->
        <div class="row">
            <!-- File Upload Information -->
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <core:icon identifier="actions-upload" size="small" />
                            File Upload
                        </h4>
                    </div>
                    <div class="panel-body">
                        <dl class="dl-horizontal">
                            <dt>File Name:</dt>
                            <dd>{result.analysis.file_name}</dd>

                            <dt>File Size:</dt>
                            <dd>{result.analysis.file_size -> f:format.bytes()}</dd>

                            <f:if condition="{result.analysis.file_type}">
                                <dt>File Type:</dt>
                                <dd>{result.analysis.file_type}</dd>
                            </f:if>

                            <dt>Upload Status:</dt>
                            <dd class="{f:if(condition: '{result.analysis.upload_error} == 0', then: 'text-success', else: 'text-danger')}">
                                <f:if condition="{result.analysis.upload_error} == 0">
                                    <f:then>✅ Successful upload</f:then>
                                    <f:else>❌ {result.analysis.upload_error_message}</f:else>
                                </f:if>
                            </dd>
                        </dl>
                    </div>
                </div>
            </div>

            <!-- CSV Data Analysis -->
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <core:icon identifier="actions-table" size="small" />
                            CSV Data Analysis
                        </h4>
                    </div>
                    <div class="panel-body">
                        <dl class="dl-horizontal">
                            <f:if condition="{result.analysis.total_lines}">
                                <dt>Total Lines:</dt>
                                <dd>{result.analysis.total_lines}</dd>

                                <dt>Header Columns:</dt>
                                <dd>{result.analysis.header_columns -> f:count()}</dd>

                                <dt>Data Rows:</dt>
                                <dd>{result.analysis.data_rows_processed}</dd>

                                <f:if condition="{result.analysis.empty_lines_skipped}">
                                    <dt>Empty Lines:</dt>
                                    <dd>{result.analysis.empty_lines_skipped} skipped</dd>
                                </f:if>

                                <dt>Line Endings:</dt>
                                <dd>
                                    <f:if condition="{result.analysis.line_endings}">
                                        <f:if condition="{result.analysis.line_endings.crlf} > 0">
                                            CRLF: {result.analysis.line_endings.crlf}
                                        </f:if>
                                        <f:if condition="{result.analysis.line_endings.cr} > 0">
                                            CR: {result.analysis.line_endings.cr}
                                        </f:if>
                                        <f:if condition="{result.analysis.line_endings.lf} > 0">
                                            LF: {result.analysis.line_endings.lf}
                                        </f:if>
                                        <f:if condition="{result.analysis.line_endings.crlf} == 0 && {result.analysis.line_endings.cr} == 0 && {result.analysis.line_endings.lf} == 0">
                                            <span class="text-danger">⚠️ None detected</span>
                                        </f:if>
                                    </f:if>
                                </dd>
                            </f:if>

                            <f:if condition="{result.analysis.detected_encoding}">
                                <dt>Encoding:</dt>
                                <dd>
                                    {result.analysis.detected_encoding}
                                    <f:if condition="{result.analysis.has_bom}">
                                        <span class="label label-info">BOM</span>
                                    </f:if>
                                </dd>
                            </f:if>

                            <f:if condition="{result.processing_time}">
                                <dt>Processing Time:</dt>
                                <dd>{result.processing_time} seconds</dd>
                            </f:if>
                        </dl>
                    </div>
                </div>
            </div>
        </div>

        <!-- Import Statistics (Success Only) -->
        <f:if condition="{result.success}">
            <div class="row">
                <div class="col-md-12">
                    <div class="panel panel-success">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <core:icon identifier="actions-check" size="small" />
                                Import Successful
                            </h4>
                        </div>
                        <div class="panel-body">
                            <div class="row">
                                <div class="col-md-4 text-center">
                                    <h3 class="text-primary">{result.imported_count}</h3>
                                    <p>Records Imported</p>
                                </div>
                                <div class="col-md-4 text-center">
                                    <h3 class="text-info">{result.translation_count}</h3>
                                    <p>Total Translations</p>
                                </div>
                                <div class="col-md-4 text-center">
                                    <h3 class="text-muted">{result.table_type -> f:format.case(mode: 'capital')}</h3>
                                    <p>Table Type</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </f:if>


        <!-- Translation Statistics Chart -->
        <f:if condition="{result.translation_stats}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <core:icon identifier="actions-localize" size="small" />
                        Translations by Language
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="translation-chart">
                        <f:variable name="maxCount" value="0" />
                        <f:for each="{result.translation_stats}" as="count">
                            <f:if condition="{count > maxCount}">
                                <f:variable name="maxCount" value="{count}" />
                            </f:if>
                        </f:for>

                        <f:for each="{result.translation_stats}" as="count" key="language">
                            <div class="translation-bar-row">
                                <div class="translation-language">
                                    <strong>{language -> f:format.case(mode: 'upper')}</strong>
                                </div>
                                <div class="translation-bar-container">
                                    <f:variable name="percentage" value="{count / maxCount * 100}" />
                                    <div class="translation-bar" style="width: {percentage}%"></div>
                                    <span class="translation-count">{count}</span>
                                </div>
                            </div>
                        </f:for>
                    </div>
                </div>
            </div>
        </f:if>

        <!-- Action Buttons -->
        <div class="panel panel-default">
            <div class="panel-body text-center">
                <a href="{returnUrl}" class="btn btn-primary">
                    <core:icon identifier="actions-view-list-expand" size="small" />
                    Back to Data Overview
                </a>
                
                <f:if condition="{result.success}">
                    <a href="{importUrl}" class="btn btn-default">
                        <core:icon identifier="actions-download" size="small" />
                        Import More Data
                    </a>
                </f:if>
                
                <f:if condition="{!result.success}">
                    <a href="{importUrl}" class="btn btn-warning">
                        <core:icon identifier="actions-edit-undo" size="small" />
                        Try Again
                    </a>
                </f:if>
            </div>
        </div>
    </div>

    <style>
        .module-icon {
            font-size: 4em;
            margin: 20px 0;
        }
        
        .translation-chart {
            margin-top: 15px;
        }
        
        .translation-bar-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .translation-language {
            width: 60px;
            text-align: right;
            margin-right: 15px;
            font-family: monospace;
        }
        
        .translation-bar-container {
            flex: 1;
            position: relative;
            background: #f5f5f5;
            border-radius: 3px;
            height: 25px;
            display: flex;
            align-items: center;
        }
        
        .translation-bar {
            background: linear-gradient(90deg, #007cba 0%, #00a0d2 100%);
            height: 100%;
            border-radius: 3px;
            min-width: 2px;
            transition: width 0.3s ease;
        }
        
        .translation-count {
            position: absolute;
            right: 8px;
            font-weight: bold;
            font-size: 12px;
            color: #333;
            z-index: 2;
        }
        
        .dl-horizontal dt {
            width: 140px;
        }
        
        .dl-horizontal dd {
            margin-left: 160px;
        }
        
        .panel-title {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .text-capitalize {
            text-transform: capitalize;
        }
    </style>
</f:section>

</html>
