<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
      xmlns:core="http://typo3.org/ns/TYPO3/CMS/Core/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Module" />

<f:section name="Content">
    <div class="landing-pages-module">
        <!-- Status Header -->
        <div class="panel panel-default">
            <div class="panel-body">
                <div class="row">
                    <div class="col-md-8">
                        <f:if condition="{result.success}">
                            <f:then>
                                <h3 class="text-success">
                                    <core:icon identifier="actions-check" size="small" />
                                    Import Successful
                                </h3>
                                <p class="text-muted">
                                    {result.message}
                                </p>
                            </f:then>
                            <f:else>
                                <h3 class="text-danger">
                                    <core:icon identifier="actions-close" size="small" />
                                    Import Failed
                                </h3>
                                <p class="text-muted">
                                    {result.message}
                                </p>
                            </f:else>
                        </f:if>
                    </div>
                    <div class="col-md-4 text-center">
                        <div class="module-icon">
                            <f:if condition="{result.success}">
                                <f:then><span class="text-success">✅</span></f:then>
                                <f:else><span class="text-danger">❌</span></f:else>
                            </f:if>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- File Analysis (always show) -->
        <div class="row">
            <!-- File Information -->
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <core:icon identifier="actions-document-info" size="small" />
                            File Analysis
                        </h4>
                    </div>
                    <div class="panel-body">
                        <dl class="dl-horizontal">
                            <dt>File Name:</dt>
                            <dd>{result.analysis.file_name}</dd>

                            <dt>File Size:</dt>
                            <dd>{result.analysis.file_size -> f:format.bytes()}</dd>

                            <dt>Upload Status:</dt>
                            <dd class="{f:if(condition: '{result.analysis.upload_error} == 0', then: 'text-success', else: 'text-danger')}">
                                <f:if condition="{result.analysis.upload_error} == 0">
                                    <f:then>✅ Successful upload</f:then>
                                    <f:else>❌ {result.analysis.upload_error_message}</f:else>
                                </f:if>
                            </dd>

                            <f:if condition="{result.analysis.total_lines}">
                                <dt>Total Lines:</dt>
                                <dd>{result.analysis.total_lines}</dd>

                                <dt>Header Columns:</dt>
                                <dd>{result.analysis.header_columns -> f:count()}</dd>

                                <dt>Data Rows:</dt>
                                <dd>{result.analysis.data_rows_processed}</dd>

                                <f:if condition="{result.analysis.empty_lines_skipped}">
                                    <dt>Empty Lines Skipped:</dt>
                                    <dd>{result.analysis.empty_lines_skipped}</dd>
                                </f:if>

                                <f:if condition="{result.analysis.line_endings}">
                                    <dt>Line Endings:</dt>
                                    <dd>
                                        <f:if condition="{result.analysis.line_endings.crlf} > 0">
                                            CRLF: {result.analysis.line_endings.crlf}
                                        </f:if>
                                        <f:if condition="{result.analysis.line_endings.cr} > 0">
                                            CR: {result.analysis.line_endings.cr}
                                        </f:if>
                                        <f:if condition="{result.analysis.line_endings.lf} > 0">
                                            LF: {result.analysis.line_endings.lf}
                                        </f:if>
                                        <f:if condition="{result.analysis.line_endings.crlf} == 0 && {result.analysis.line_endings.cr} == 0 && {result.analysis.line_endings.lf} == 0">
                                            <span class="text-danger">⚠️ No line endings detected - file may be malformed</span>
                                        </f:if>
                                    </dd>
                                </f:if>
                            </f:if>

                            <f:if condition="{result.processing_time}">
                                <dt>Processing Time:</dt>
                                <dd>{result.processing_time} seconds</dd>
                            </f:if>
                        </dl>
                    </div>
                </div>
            </div>

        <f:if condition="{result.success}">
            <!-- Import Statistics -->
            <div class="col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <core:icon identifier="actions-chart-bar" size="small" />
                            Import Statistics
                        </h4>
                    </div>
                    <div class="panel-body">
                        <dl class="dl-horizontal">
                            <dt>Records Imported:</dt>
                            <dd class="text-primary"><strong>{result.imported_count}</strong></dd>

                            <dt>Total Translations:</dt>
                            <dd class="text-info"><strong>{result.translation_count}</strong></dd>

                            <dt>Table Type:</dt>
                            <dd class="text-capitalize">{result.table_type}</dd>
                        </dl>
                    </div>
                </div>
            </div>
        </f:if>

        <f:if condition="{result.success} == 0">
            <!-- Error Details -->
            <div class="col-md-6">
                <div class="panel panel-danger">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <core:icon identifier="actions-exclamation-triangle" size="small" />
                            Error Details
                        </h4>
                    </div>
                    <div class="panel-body">
                        <dl class="dl-horizontal">
                            <dt>Error Type:</dt>
                            <dd class="text-danger"><strong>{result.analysis.error_type}</strong></dd>

                            <dt>Error Message:</dt>
                            <dd class="text-danger">{result.analysis.error_message}</dd>

                            <f:if condition="{result.analysis.header_columns}">
                                <dt>Detected Columns:</dt>
                                <dd>
                                    <f:for each="{result.analysis.header_columns}" as="column" iteration="iteration">
                                        <code>{column}</code><f:if condition="{iteration.isLast}"><f:else>, </f:else></f:if>
                                    </f:for>
                                </dd>
                            </f:if>

                            <f:if condition="{result.analysis.detected_translations}">
                                <dt>Translation Columns:</dt>
                                <dd>
                                    <f:for each="{result.analysis.detected_translations}" as="translation" key="column">
                                        <span class="label label-info">{translation.lang_code}</span>
                                    </f:for>
                                </dd>
                            </f:if>

                            <f:if condition="{result.analysis.debug_info}">
                                <dt>Debug Info:</dt>
                                <dd>
                                    <small>
                                        Method: {result.analysis.debug_info.request_method}<br>
                                        Content-Type: {result.analysis.debug_info.content_type}<br>
                                        Files Count: {result.analysis.debug_info.uploaded_files_count}<br>
                                        File Keys: <f:for each="{result.analysis.debug_info.uploaded_files_keys}" as="key" iteration="iteration">{key}<f:if condition="{iteration.isLast}"><f:else>, </f:else></f:if></f:for><br>
                                        Table: {result.analysis.debug_info.table_param}<br>
                                        Confirmed: {result.analysis.debug_info.confirm_param}
                                    </small>
                                </dd>
                            </f:if>
                        </dl>
                    </div>
                </div>
            </div>
        </f:if>
        </div>

        <!-- Translation Statistics Chart -->
        <f:if condition="{result.translation_stats}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <core:icon identifier="actions-localize" size="small" />
                        Translations by Language
                    </h4>
                </div>
                <div class="panel-body">
                    <div class="translation-chart">
                        <f:variable name="maxCount" value="0" />
                        <f:for each="{result.translation_stats}" as="count">
                            <f:if condition="{count > maxCount}">
                                <f:variable name="maxCount" value="{count}" />
                            </f:if>
                        </f:for>

                        <f:for each="{result.translation_stats}" as="count" key="language">
                            <div class="translation-bar-row">
                                <div class="translation-language">
                                    <strong>{language -> f:format.case(mode: 'upper')}</strong>
                                </div>
                                <div class="translation-bar-container">
                                    <f:variable name="percentage" value="{count / maxCount * 100}" />
                                    <div class="translation-bar" style="width: {percentage}%"></div>
                                    <span class="translation-count">{count}</span>
                                </div>
                            </div>
                        </f:for>
                    </div>
                </div>
            </div>
        </f:if>

        <!-- Action Buttons -->
        <div class="panel panel-default">
            <div class="panel-body text-center">
                <a href="{returnUrl}" class="btn btn-primary">
                    <core:icon identifier="actions-view-list-expand" size="small" />
                    Back to Data Overview
                </a>
                
                <f:if condition="{result.success}">
                    <a href="{importUrl}" class="btn btn-default">
                        <core:icon identifier="actions-download" size="small" />
                        Import More Data
                    </a>
                </f:if>
                
                <f:if condition="{!result.success}">
                    <a href="{importUrl}" class="btn btn-warning">
                        <core:icon identifier="actions-edit-undo" size="small" />
                        Try Again
                    </a>
                </f:if>
            </div>
        </div>
    </div>

    <style>
        .module-icon {
            font-size: 4em;
            margin: 20px 0;
        }
        
        .translation-chart {
            margin-top: 15px;
        }
        
        .translation-bar-row {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 5px 0;
        }
        
        .translation-language {
            width: 60px;
            text-align: right;
            margin-right: 15px;
            font-family: monospace;
        }
        
        .translation-bar-container {
            flex: 1;
            position: relative;
            background: #f5f5f5;
            border-radius: 3px;
            height: 25px;
            display: flex;
            align-items: center;
        }
        
        .translation-bar {
            background: linear-gradient(90deg, #007cba 0%, #00a0d2 100%);
            height: 100%;
            border-radius: 3px;
            min-width: 2px;
            transition: width 0.3s ease;
        }
        
        .translation-count {
            position: absolute;
            right: 8px;
            font-weight: bold;
            font-size: 12px;
            color: #333;
            z-index: 2;
        }
        
        .dl-horizontal dt {
            width: 140px;
        }
        
        .dl-horizontal dd {
            margin-left: 160px;
        }
        
        .panel-title {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .text-capitalize {
            text-transform: capitalize;
        }
    </style>
</f:section>

</html>
