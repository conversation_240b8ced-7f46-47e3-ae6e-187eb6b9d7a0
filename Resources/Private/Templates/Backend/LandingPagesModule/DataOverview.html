<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers"
      xmlns:be="http://typo3.org/ns/TYPO3/CMS/Backend/ViewHelpers"
      xmlns:core="http://typo3.org/ns/TYPO3/CMS/Core/ViewHelpers"
      data-namespace-typo3-fluid="true">

<f:layout name="Module" />

<f:section name="Content">
    <div class="landing-pages-module">
        <!-- Description -->
        <div class="alert alert-info">
            <strong>Reference Data Overview</strong><br>
            This section shows statistics and management options for the reference data tables used by the Flight Landing Pages extension.
        </div>

        <f:if condition="{dataStatistics.error}">
            <f:then>
                <div class="alert alert-danger">
                    <strong>Error:</strong> {dataStatistics.error}
                </div>
            </f:then>
            <f:else>
                <!-- Data Tables Cards -->
                <div class="data-tables-list">
                    <f:for each="{dataStatistics}" as="tableData" key="tableName">
                        <div class="panel panel-default data-table-card">
                            <div class="panel-body">
                                <!-- Main Content Row -->
                                <div class="row">
                                    <!-- Table Info -->
                                    <div class="col-md-8">
                                        <h4 class="table-title">
                                            <core:icon identifier="{tableData.icon}" size="small" />
                                            {tableData.title}
                                        </h4>
                                        <p class="text-muted">{tableData.description}</p>
                                    </div>

                                    <!-- Statistics -->
                                    <div class="col-md-4">
                                        <div class="data-stat">
                                            <h4 class="text-primary">{tableData.totalRecords}</h4>
                                            <small class="text-muted">Total Records</small>
                                        </div>

                                        <!-- Translations Chart -->
                                        <f:if condition="{tableData.translations}">
                                            <div class="translations-section">
                                                <small class="text-muted">Translations by Language:</small>
                                                <div class="translation-chart">
                                                    <f:variable name="maxCount" value="0" />
                                                    <f:for each="{tableData.translations}" as="translation">
                                                        <f:if condition="{translation.count > maxCount}">
                                                            <f:variable name="maxCount" value="{translation.count}" />
                                                        </f:if>
                                                    </f:for>

                                                    <f:for each="{tableData.translations}" as="translation">
                                                        <div class="translation-bar">
                                                            <div class="translation-label">
                                                                <span class="lang-code">{translation.lang}</span>
                                                                <span class="lang-count">{translation.count}</span>
                                                            </div>
                                                            <div class="translation-progress">
                                                                <f:variable name="percentage" value="{translation.count / maxCount * 100}" />
                                                                <div class="translation-fill" style="width: {percentage}%"></div>
                                                            </div>
                                                        </div>
                                                    </f:for>
                                                </div>
                                            </div>
                                        </f:if>
                                    </div>
                                </div>

                                <!-- Action Buttons Row -->
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="action-buttons">
                                            <div class="btn-group btn-group-sm" role="group">
                                                <a href="{tableData.importUrl}" class="btn btn-default">
                                                    <core:icon identifier="actions-download" size="small" />
                                                    Import
                                                </a>
                                                <button type="button" class="btn btn-default" disabled>
                                                    <core:icon identifier="actions-upload" size="small" />
                                                    Export
                                                </button>
                                                <button type="button" class="btn btn-default" disabled>
                                                    <core:icon identifier="actions-list" size="small" />
                                                    List
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </f:for>
                </div>


            </f:else>
        </f:if>
    </div>

    <style>
        .data-tables-list {
            margin-top: 16px;
        }

        .data-table-card {
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.12), 0 1px 2px rgba(0,0,0,0.24);
            border: none;
            border-radius: 4px;
        }

        .data-table-card .panel-body {
            padding: 16px;
            padding-bottom: 0;
            margin-bottom: 16px;
        }

        .table-title {
            margin: 0 0 8px 0;
            color: #333;
            display: flex;
            align-items: center;
            font-size: 1.1em;
            font-weight: 500;
        }

        .table-title .icon {
            margin-right: 8px;
            opacity: 0.7;
        }

        .data-stat {
            text-align: center;
            margin-bottom: 8px;
        }

        .data-stat h4 {
            margin: 0;
            font-size: 2em;
            font-weight: 600;
            line-height: 1.2;
        }

        .data-stat small {
            font-size: 0.75em;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .translations-section {
            margin-top: 8px;
        }

        .translations-section small {
            font-size: 0.75em;
            font-weight: 500;
            color: #666;
            margin-bottom: 8px;
            display: block;
        }

        .translation-chart {
            margin-top: 8px;
        }

        .translation-bar {
            margin-bottom: 6px;
        }

        .translation-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 2px;
            font-size: 0.7em;
        }

        .lang-code {
            font-weight: 600;
            text-transform: uppercase;
            color: #333;
        }

        .lang-count {
            color: #666;
            font-weight: 500;
        }

        .translation-progress {
            height: 6px;
            background-color: #e0e0e0;
            border-radius: 3px;
            overflow: hidden;
        }

        .translation-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #2196F3);
            border-radius: 3px;
            transition: width 0.3s ease-in-out;
        }

        .action-buttons {
            margin: 0;
            padding-top: 12px;
        }

        .btn-group .btn {
            margin-right: 4px;
            padding: 4px 8px;
            font-size: 0.8em;
            border-radius: 3px;
            text-transform: uppercase;
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        .btn-group .btn:last-child {
            margin-right: 0;
        }

        .btn .icon {
            margin-right: 4px;
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        /* Material-like hover effects */
        .data-table-card:hover {
            box-shadow: 0 3px 6px rgba(0,0,0,0.16), 0 3px 6px rgba(0,0,0,0.23);
            transition: box-shadow 0.2s ease-in-out;
        }

        .btn:not(:disabled):hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
            transition: all 0.2s ease-in-out;
        }

        /* Compact description text */
        .data-table-card p.text-muted {
            margin-bottom: 8px;
            font-size: 0.85em;
            line-height: 1.4;
        }
    </style>
</f:section>

</html>
