<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Controller\Backend;

use Bgs\LandingPages\Domain\Repository\FlightRouteRepository;
use Bgs\LandingPages\Service\VirtualRouteService;
use Bgs\LandingPages\Service\Import\ImportProcessorFactory;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Backend\Attribute\AsController;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Backend\Template\Components\ButtonBar;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Core\Database\ConnectionPool;

use TYPO3\CMS\Core\Http\RedirectResponse;
use TYPO3\CMS\Core\Imaging\IconFactory;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Messaging\FlashMessageService;
use TYPO3\CMS\Core\Page\PageRenderer;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Backend Module Controller for Landing Pages
 */
#[AsController]
class LandingPagesModuleController
{
    public function __construct(
        private readonly ModuleTemplateFactory $moduleTemplateFactory,
        private readonly FlightRouteRepository $flightRouteRepository,
        private readonly VirtualRouteService $virtualRouteService,
        private readonly PageRenderer $pageRenderer,
        private readonly IconFactory $iconFactory,
        private readonly UriBuilder $uriBuilder
    ) {
    }



    /**
     * Main module index action
     */
    public function indexAction(ServerRequestInterface $request): ResponseInterface
    {

        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('Landing Pages');

        // Add CSS for module styling
        $this->pageRenderer->addCssFile('EXT:landing-pages/Resources/Public/Css/Backend/LandingPagesModule.css');

        // Add navigation buttons
        $this->addNavigationButtons($moduleTemplate, 'index');

        // Add some basic statistics
        $statistics = $this->getStatistics();

        $moduleTemplate->assignMultiple([
            'statistics' => $statistics,
            'extensionVersion' => $this->getExtensionVersion(),
        ]);

        return $moduleTemplate->renderResponse('Backend/LandingPagesModule/Index');
    }

    /**
     * Data Overview action showing reference data tables information
     */
    public function dataOverviewAction(ServerRequestInterface $request): ResponseInterface
    {

        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('Landing Pages - Data Overview');

        // Add CSS for module styling
        $this->pageRenderer->addCssFile('EXT:landing-pages/Resources/Public/Css/Backend/LandingPagesModule.css');

        // Add navigation buttons
        $this->addNavigationButtons($moduleTemplate, 'dataOverview');

        // Get reference data statistics
        $dataStatistics = $this->getDataStatistics();

        $moduleTemplate->assignMultiple([
            'dataStatistics' => $dataStatistics,
            'extensionVersion' => $this->getExtensionVersion(),
        ]);

        return $moduleTemplate->renderResponse('Backend/LandingPagesModule/DataOverview');
    }

    /**
     * Get basic statistics for the module
     */
    private function getStatistics(): array
    {
        try {
            $totalRoutes = $this->flightRouteRepository->countAll();
            $activeRoutes = $this->flightRouteRepository->countByActive(true);

            return [
                'totalRoutes' => $totalRoutes,
                'activeRoutes' => $activeRoutes,
                'inactiveRoutes' => $totalRoutes - $activeRoutes,
            ];
        } catch (\Exception $e) {
            // Return default values if there's an error
            return [
                'totalRoutes' => 0,
                'activeRoutes' => 0,
                'inactiveRoutes' => 0,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get reference data statistics for data overview
     */
    private function getDataStatistics(): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);

        try {
            // Airports statistics
            $airportsData = $this->getTableStatistics('tx_landingpages_airports', 'code');
            $airportsTranslations = $this->getTranslationStatistics('tx_landingpages_airports_i18n', 'code');

            // Countries statistics
            $countriesData = $this->getTableStatistics('tx_landingpages_countries', 'code');
            $countriesTranslations = $this->getTranslationStatistics('tx_landingpages_countries_i18n', 'code');

            // Cities statistics
            $citiesData = $this->getTableStatistics('tx_landingpages_cities', 'id');
            $citiesTranslations = $this->getTranslationStatistics('tx_landingpages_cities_i18n', 'id');

            return [
                'airports' => [
                    'title' => 'Airports',
                    'icon' => 'actions-move-up',
                    'totalRecords' => $airportsData['count'],
                    'translations' => $airportsTranslations,
                    'description' => 'Airport reference data with IATA codes and geographic information',
                    'importUrl' => (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.importForm', ['table' => 'airports'])
                ],
                'countries' => [
                    'title' => 'Countries',
                    'icon' => 'apps-pagetree-folder-default',
                    'totalRecords' => $countriesData['count'],
                    'translations' => $countriesTranslations,
                    'description' => 'Country reference data with ISO codes and phone codes',
                    'importUrl' => (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.importForm', ['table' => 'countries'])
                ],
                'cities' => [
                    'title' => 'Cities',
                    'icon' => 'apps-pagetree-page-default',
                    'totalRecords' => $citiesData['count'],
                    'translations' => $citiesTranslations,
                    'description' => 'City reference data with regional information and sorting',
                    'importUrl' => (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.importForm', ['table' => 'cities'])
                ]
            ];
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get basic table statistics
     */
    private function getTableStatistics(string $tableName, string $primaryKey): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $queryBuilder = $connectionPool->getQueryBuilderForTable($tableName);

        $count = $queryBuilder
            ->count($primaryKey)
            ->from($tableName)
            ->executeQuery()
            ->fetchOne();

        return ['count' => (int)$count];
    }

    /**
     * Get translation statistics by language
     */
    private function getTranslationStatistics(string $tableName, string $foreignKey): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $queryBuilder = $connectionPool->getQueryBuilderForTable($tableName);

        $translations = $queryBuilder
            ->select('lang')
            ->addSelectLiteral('COUNT(' . $queryBuilder->quoteIdentifier($foreignKey) . ') as count')
            ->from($tableName)
            ->groupBy('lang')
            ->orderBy('count', 'DESC')
            ->executeQuery()
            ->fetchAllAssociative();

        return $translations ?: [];
    }

    /**
     * Get extension version from ext_emconf.php
     */
    private function getExtensionVersion(): string
    {
        $extEmconfPath = \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::extPath('landing-pages') . 'ext_emconf.php';
        if (file_exists($extEmconfPath)) {
            $EM_CONF = [];
            include $extEmconfPath;
            return $EM_CONF['landing-pages']['version'] ?? '1.0.0';
        }
        return '1.0.0';
    }

    /**
     * Add navigation buttons to the module template
     */
    private function addNavigationButtons($moduleTemplate, string $currentAction): void
    {
        $buttonBar = $moduleTemplate->getDocHeaderComponent()->getButtonBar();

        // Dashboard button
        $dashboardButton = $buttonBar->makeLinkButton()
            ->setTitle('Dashboard')
            ->setIcon($this->iconFactory->getIcon('module-landing-pages', 'small'))
            ->setHref($this->uriBuilder->buildUriFromRoute('web_landingpages'))
            ->setShowLabelText(false);

        // Data Overview button (Database/Data view)
        $dataOverviewButton = $buttonBar->makeLinkButton()
            ->setTitle('Data Overview')
            ->setIcon($this->iconFactory->getIcon('actions-database-import', 'small'))
            ->setHref($this->uriBuilder->buildUriFromRoute('web_landingpages.dataOverview'))
            ->setShowLabelText(false);

        // Add buttons with proper grouping
        $buttonBar->addButton($dashboardButton, ButtonBar::BUTTON_POSITION_LEFT, 1);
        $buttonBar->addButton($dataOverviewButton, ButtonBar::BUTTON_POSITION_LEFT, 1);
    }

    /**
     * Import form action
     */
    public function importFormAction(ServerRequestInterface $request): ResponseInterface
    {

        $queryParams = $request->getQueryParams();
        $table = $queryParams['table'] ?? '';
        $returnUrl = $queryParams['returnUrl'] ?? (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.dataOverview');

        // Validate table type
        if (!ImportProcessorFactory::isValidTableType($table)) {
            throw new \InvalidArgumentException('Invalid table type: ' . $table);
        }

        // Get processor and table config
        $processor = ImportProcessorFactory::createProcessor($table);
        $tableConfig = $processor->getTableConfig();

        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('Import ' . $tableConfig['title'] . ' Data');

        // Add CSS for module styling
        $this->pageRenderer->addCssFile('EXT:landing-pages/Resources/Public/Css/Backend/LandingPagesModule.css');

        // Add JavaScript for import form functionality
        $this->pageRenderer->addJsFile('EXT:landing-pages/Resources/Public/JavaScript/Backend/ImportForm.js');

        // Add close button to doc header
        $buttonBar = $moduleTemplate->getDocHeaderComponent()->getButtonBar();
        $closeButton = $buttonBar->makeLinkButton()
            ->setTitle('Close')
            ->setIcon($this->iconFactory->getIcon('actions-close', 'small'))
            ->setHref($returnUrl)
            ->setShowLabelText(true);
        $buttonBar->addButton($closeButton, ButtonBar::BUTTON_POSITION_LEFT, 1);

        // Generate import action URL
        $importActionUrl = (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.import');

        $moduleTemplate->assignMultiple([
            'table' => $table,
            'tableConfig' => $tableConfig,
            'returnUrl' => $returnUrl,
            'importActionUrl' => $importActionUrl,
            'errorMessage' => $queryParams['error'] ?? null,
            'successMessage' => $queryParams['success'] ?? null,
        ]);

        return $moduleTemplate->renderResponse('Backend/LandingPagesModule/ImportForm');
    }

    /**
     * Import action
     */
    public function importAction(ServerRequestInterface $request): ResponseInterface
    {
        // Get data from either POST or GET request
        $parsedBody = $request->getParsedBody() ?: [];
        $queryParams = $request->getQueryParams();

        $table = $parsedBody['table'] ?? $queryParams['table'] ?? '';
        $returnUrl = $parsedBody['returnUrl'] ?? $queryParams['returnUrl'] ?? (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.dataOverview');
        $confirmReplace = $parsedBody['confirmReplace'] ?? $queryParams['confirmReplace'] ?? false;

        // Initialize csvFile variable
        $csvFile = null;

        // Debug logging
        error_log('=== IMPORT ACTION DEBUG ===');
        error_log('Request method: ' . $request->getMethod());
        error_log('Content type: ' . $request->getHeaderLine('Content-Type'));
        error_log('Table: ' . $table);
        error_log('Return URL: ' . $returnUrl);
        error_log('Confirm replace: ' . ($confirmReplace ? 'yes' : 'no'));

        try {
            // Check if this is actually a file upload request
            $uploadedFiles = $request->getUploadedFiles();
            error_log('Uploaded files count: ' . count($uploadedFiles));
            error_log('Uploaded files keys: ' . implode(', ', array_keys($uploadedFiles)));

            if (empty($uploadedFiles)) {
                error_log('ERROR: No uploaded files found');
                throw new \Exception('No file upload data found. Request method: ' . $request->getMethod() . ', Content-Type: ' . $request->getHeaderLine('Content-Type'));
            }
            // Validate table type
            if (!ImportProcessorFactory::isValidTableType($table)) {
                throw new \InvalidArgumentException('Invalid table type: ' . $table);
            }

            // Check confirmation
            if (!$confirmReplace) {
                throw new \Exception('Import confirmation is required');
            }

            // Get the uploaded CSV file
            $csvFile = $uploadedFiles['csvFile'] ?? null;

            if (!$csvFile || $csvFile->getError() !== UPLOAD_ERR_OK) {
                throw new \Exception('No valid CSV file uploaded');
            }

            // Validate file size (500MB limit)
            if ($csvFile->getSize() > 500 * 1024 * 1024) {
                throw new \Exception('File size exceeds 500MB limit');
            }

            // Validate file type
            $fileName = $csvFile->getClientFilename();
            if (!$fileName || !str_ends_with(strtolower($fileName), '.csv')) {
                throw new \Exception('Only CSV files are allowed');
            }

            // Get processor and import data
            $processor = ImportProcessorFactory::createProcessor($table);
            $result = $processor->processImport($csvFile);

            // Store result in session to avoid URL length issues
            $sessionKey = 'import_result_' . uniqid();
            $_SESSION[$sessionKey] = $result;

            // Redirect to status page with session key
            $statusUrl = (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.importStatus', [
                'table' => $table,
                'returnUrl' => $returnUrl,
                'sessionKey' => $sessionKey
            ]);

            // Debug: Log the generated URL
            error_log('Generated status URL: ' . $statusUrl);

            return $this->redirectToUri($statusUrl);

        } catch (\Exception $e) {
            // Create detailed error result with file analysis
            $errorResult = [
                'success' => false,
                'message' => $e->getMessage(),
                'table_type' => $table,
                'file_name' => $csvFile ? $csvFile->getClientFilename() : 'Unknown',
                'file_size' => $csvFile ? $csvFile->getSize() : 0,
                'processing_time' => 0,
                'imported_count' => 0,
                'translation_count' => 0,
                'translation_stats' => [],
                'analysis' => [
                    'file_name' => $csvFile ? $csvFile->getClientFilename() : 'Unknown',
                    'file_size' => $csvFile ? $csvFile->getSize() : 0,
                    'upload_error' => $csvFile ? $csvFile->getError() : UPLOAD_ERR_NO_FILE,
                    'upload_error_message' => $csvFile ? $this->getUploadErrorMessage($csvFile->getError()) : 'No file uploaded',
                    'error_message' => $e->getMessage(),
                    'error_type' => $this->categorizeError($e->getMessage())
                ]
            ];

            // Store error result in session
            $sessionKey = 'import_result_' . uniqid();
            $_SESSION[$sessionKey] = $errorResult;

            // Redirect to status page with error
            $statusUrl = (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.importStatus', [
                'table' => $table,
                'returnUrl' => $returnUrl,
                'sessionKey' => $sessionKey
            ]);

            return $this->redirectToUri($statusUrl);
        }
    }

    /**
     * Import status action
     */
    public function importStatusAction(ServerRequestInterface $request): ResponseInterface
    {

        $queryParams = $request->getQueryParams();
        $table = $queryParams['table'] ?? '';
        $returnUrl = $queryParams['returnUrl'] ?? (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.dataOverview');
        $sessionKey = $queryParams['sessionKey'] ?? '';

        // Get result data from session
        $resultData = [];
        if ($sessionKey && isset($_SESSION[$sessionKey])) {
            $resultData = $_SESSION[$sessionKey];
            // Clean up session data
            unset($_SESSION[$sessionKey]);
        } else {
            // Fallback if session data is missing
            $resultData = [
                'success' => false,
                'message' => 'Import status data not found. Please try the import again.',
                'table_type' => $table,
                'file_name' => 'Unknown',
                'file_size' => 0,
                'processing_time' => 0,
                'imported_count' => 0,
                'translation_count' => 0,
                'translation_stats' => []
            ];
        }

        // Validate table type for import URL generation
        $importUrl = '';
        if (ImportProcessorFactory::isValidTableType($table)) {
            $importUrl = (string)$this->uriBuilder->buildUriFromRoute('web_landingpages.importForm', ['table' => $table]);
        }

        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('Import Status - ' . ucfirst($table));

        // Add CSS for module styling
        $this->pageRenderer->addCssFile('EXT:landing-pages/Resources/Public/Css/Backend/LandingPagesModule.css');

        // Add close button to doc header
        $buttonBar = $moduleTemplate->getDocHeaderComponent()->getButtonBar();
        $backButton = $buttonBar->makeLinkButton()
            ->setTitle('Back to Data Overview')
            ->setIcon($this->iconFactory->getIcon('actions-view-list-expand', 'small'))
            ->setHref($returnUrl)
            ->setShowLabelText(true);
        $buttonBar->addButton($backButton, ButtonBar::BUTTON_POSITION_LEFT, 1);

        $moduleTemplate->assignMultiple([
            'result' => $resultData,
            'table' => $table,
            'returnUrl' => $returnUrl,
            'importUrl' => $importUrl,
        ]);

        return $moduleTemplate->renderResponse('Backend/LandingPagesModule/ImportStatus');
    }

    /**
     * Add flash message
     */
    private function addFlashMessage(string $message, string $title, ContextualFeedbackSeverity $severity): void
    {
        $flashMessage = GeneralUtility::makeInstance(
            FlashMessage::class,
            $message,
            $title,
            $severity,
            true
        );

        $flashMessageService = GeneralUtility::makeInstance(FlashMessageService::class);
        $messageQueue = $flashMessageService->getMessageQueueByIdentifier();
        $messageQueue->addMessage($flashMessage);
    }

    /**
     * Redirect to URI
     */
    private function redirectToUri(string $uri): ResponseInterface
    {
        $response = GeneralUtility::makeInstance(RedirectResponse::class, $uri, 302);
        $response = $response->withHeader('X-Frame-Options', 'SAMEORIGIN');
        $response = $response->withHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
        return $response;
    }

    /**
     * Get human-readable upload error message
     */
    private function getUploadErrorMessage(int $errorCode): string
    {
        switch ($errorCode) {
            case UPLOAD_ERR_OK:
                return 'No error';
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }

    /**
     * Categorize error type for better user feedback
     */
    private function categorizeError(string $errorMessage): string
    {
        if (strpos($errorMessage, 'upload') !== false) {
            return 'File Upload Error';
        } elseif (strpos($errorMessage, 'CSV') !== false || strpos($errorMessage, 'column') !== false) {
            return 'CSV Format Error';
        } elseif (strpos($errorMessage, 'Database') !== false || strpos($errorMessage, 'SQL') !== false) {
            return 'Database Error';
        } elseif (strpos($errorMessage, 'validation') !== false || strpos($errorMessage, 'Invalid') !== false) {
            return 'Data Validation Error';
        } else {
            return 'General Error';
        }
    }


}
