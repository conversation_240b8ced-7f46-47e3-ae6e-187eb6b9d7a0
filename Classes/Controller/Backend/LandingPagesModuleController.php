<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Controller\Backend;

use Bgs\LandingPages\Domain\Repository\FlightRouteRepository;
use Bgs\LandingPages\Service\VirtualRouteService;
use Bgs\LandingPages\Service\Import\ImportProcessorFactory;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\ServerRequestInterface;
use TYPO3\CMS\Backend\Attribute\AsController;
use TYPO3\CMS\Backend\Routing\UriBuilder;
use TYPO3\CMS\Backend\Template\Components\ButtonBar;
use TYPO3\CMS\Backend\Template\ModuleTemplateFactory;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Http\MethodNotAllowedException;
use TYPO3\CMS\Core\Imaging\IconFactory;
use TYPO3\CMS\Core\Messaging\FlashMessage;
use TYPO3\CMS\Core\Page\PageRenderer;
use TYPO3\CMS\Core\Type\ContextualFeedbackSeverity;
use TYPO3\CMS\Core\Utility\GeneralUtility;

/**
 * Backend Module Controller for Landing Pages
 */
#[AsController]
class LandingPagesModuleController
{
    public function __construct(
        private readonly ModuleTemplateFactory $moduleTemplateFactory,
        private readonly FlightRouteRepository $flightRouteRepository,
        private readonly VirtualRouteService $virtualRouteService,
        private readonly PageRenderer $pageRenderer,
        private readonly IconFactory $iconFactory,
        private readonly UriBuilder $uriBuilder
    ) {
    }



    /**
     * Main module index action
     */
    public function indexAction(ServerRequestInterface $request): ResponseInterface
    {
        if ($request->getMethod() !== 'GET') {
            throw new MethodNotAllowedException('Method not allowed', 405);
        }

        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('Landing Pages');

        // Add CSS for module styling
        $this->pageRenderer->addCssFile('EXT:landing-pages/Resources/Public/Css/Backend/LandingPagesModule.css');

        // Add navigation buttons
        $this->addNavigationButtons($moduleTemplate, 'index');

        // Add some basic statistics
        $statistics = $this->getStatistics();

        $moduleTemplate->assignMultiple([
            'statistics' => $statistics,
            'extensionVersion' => $this->getExtensionVersion(),
        ]);

        return $moduleTemplate->renderResponse('Backend/LandingPagesModule/Index');
    }

    /**
     * Data Overview action showing reference data tables information
     */
    public function dataOverviewAction(ServerRequestInterface $request): ResponseInterface
    {
        if ($request->getMethod() !== 'GET') {
            throw new MethodNotAllowedException('Method not allowed', 405);
        }

        $moduleTemplate = $this->moduleTemplateFactory->create($request);
        $moduleTemplate->setTitle('Landing Pages - Data Overview');

        // Add CSS for module styling
        $this->pageRenderer->addCssFile('EXT:landing-pages/Resources/Public/Css/Backend/LandingPagesModule.css');

        // Add navigation buttons
        $this->addNavigationButtons($moduleTemplate, 'dataOverview');

        // Get reference data statistics
        $dataStatistics = $this->getDataStatistics();

        $moduleTemplate->assignMultiple([
            'dataStatistics' => $dataStatistics,
            'extensionVersion' => $this->getExtensionVersion(),
        ]);

        return $moduleTemplate->renderResponse('Backend/LandingPagesModule/DataOverview');
    }

    /**
     * Get basic statistics for the module
     */
    private function getStatistics(): array
    {
        try {
            $totalRoutes = $this->flightRouteRepository->countAll();
            $activeRoutes = $this->flightRouteRepository->countByActive(true);

            return [
                'totalRoutes' => $totalRoutes,
                'activeRoutes' => $activeRoutes,
                'inactiveRoutes' => $totalRoutes - $activeRoutes,
            ];
        } catch (\Exception $e) {
            // Return default values if there's an error
            return [
                'totalRoutes' => 0,
                'activeRoutes' => 0,
                'inactiveRoutes' => 0,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get reference data statistics for data overview
     */
    private function getDataStatistics(): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);

        try {
            // Airports statistics
            $airportsData = $this->getTableStatistics('tx_landingpages_airports', 'code');
            $airportsTranslations = $this->getTranslationStatistics('tx_landingpages_airports_i18n', 'code');

            // Countries statistics
            $countriesData = $this->getTableStatistics('tx_landingpages_countries', 'code');
            $countriesTranslations = $this->getTranslationStatistics('tx_landingpages_countries_i18n', 'code');

            // Cities statistics
            $citiesData = $this->getTableStatistics('tx_landingpages_cities', 'id');
            $citiesTranslations = $this->getTranslationStatistics('tx_landingpages_cities_i18n', 'id');

            return [
                'airports' => [
                    'title' => 'Airports',
                    'icon' => 'actions-move-up',
                    'totalRecords' => $airportsData['count'],
                    'translations' => $airportsTranslations,
                    'description' => 'Airport reference data with IATA codes and geographic information'
                ],
                'countries' => [
                    'title' => 'Countries',
                    'icon' => 'apps-pagetree-folder-default',
                    'totalRecords' => $countriesData['count'],
                    'translations' => $countriesTranslations,
                    'description' => 'Country reference data with ISO codes and phone codes'
                ],
                'cities' => [
                    'title' => 'Cities',
                    'icon' => 'apps-pagetree-page-default',
                    'totalRecords' => $citiesData['count'],
                    'translations' => $citiesTranslations,
                    'description' => 'City reference data with regional information and sorting'
                ]
            ];
        } catch (\Exception $e) {
            return [
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get basic table statistics
     */
    private function getTableStatistics(string $tableName, string $primaryKey): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $queryBuilder = $connectionPool->getQueryBuilderForTable($tableName);

        $count = $queryBuilder
            ->count($primaryKey)
            ->from($tableName)
            ->executeQuery()
            ->fetchOne();

        return ['count' => (int)$count];
    }

    /**
     * Get translation statistics by language
     */
    private function getTranslationStatistics(string $tableName, string $foreignKey): array
    {
        $connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
        $queryBuilder = $connectionPool->getQueryBuilderForTable($tableName);

        $translations = $queryBuilder
            ->select('lang')
            ->addSelectLiteral('COUNT(' . $queryBuilder->quoteIdentifier($foreignKey) . ') as count')
            ->from($tableName)
            ->groupBy('lang')
            ->orderBy('count', 'DESC')
            ->executeQuery()
            ->fetchAllAssociative();

        return $translations ?: [];
    }

    /**
     * Get extension version from ext_emconf.php
     */
    private function getExtensionVersion(): string
    {
        $extEmconfPath = \TYPO3\CMS\Core\Utility\ExtensionManagementUtility::extPath('landing-pages') . 'ext_emconf.php';
        if (file_exists($extEmconfPath)) {
            $EM_CONF = [];
            include $extEmconfPath;
            return $EM_CONF['landing-pages']['version'] ?? '1.0.0';
        }
        return '1.0.0';
    }

    /**
     * Add navigation buttons to the module template
     */
    private function addNavigationButtons($moduleTemplate, string $currentAction): void
    {
        $buttonBar = $moduleTemplate->getDocHeaderComponent()->getButtonBar();

        // Dashboard button
        $dashboardButton = $buttonBar->makeLinkButton()
            ->setTitle('Dashboard')
            ->setIcon($this->iconFactory->getIcon('module-landing-pages', 'small'))
            ->setHref($this->uriBuilder->buildUriFromRoute('web_landingpages'))
            ->setShowLabelText(false);

        // Data Overview button (Database/Data view)
        $dataOverviewButton = $buttonBar->makeLinkButton()
            ->setTitle('Data Overview')
            ->setIcon($this->iconFactory->getIcon('actions-database-import', 'small'))
            ->setHref($this->uriBuilder->buildUriFromRoute('web_landingpages.dataOverview'))
            ->setShowLabelText(false);

        // Add buttons with proper grouping
        $buttonBar->addButton($dashboardButton, ButtonBar::BUTTON_POSITION_LEFT, 1);
        $buttonBar->addButton($dataOverviewButton, ButtonBar::BUTTON_POSITION_LEFT, 1);
    }


}
