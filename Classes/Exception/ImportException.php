<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Exception;

/**
 * Custom exception for import operations that can carry analysis data
 */
class ImportException extends \Exception
{
    private array $analysisData = [];

    public function __construct(string $message = "", int $code = 0, ?\Throwable $previous = null, array $analysisData = [])
    {
        parent::__construct($message, $code, $previous);
        $this->analysisData = $analysisData;
    }

    public function getAnalysisData(): array
    {
        return $this->analysisData;
    }

    public function setAnalysisData(array $analysisData): void
    {
        $this->analysisData = $analysisData;
    }
}
