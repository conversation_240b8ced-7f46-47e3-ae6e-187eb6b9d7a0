<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Service\Import;

/**
 * Import processor for airports data
 */
class AirportsImportProcessor extends AbstractImportProcessor
{
    protected function getMainTableName(): string
    {
        return 'tx_landingpages_airports';
    }

    protected function getTranslationTableName(): string
    {
        return 'tx_landingpages_airports_i18n';
    }

    public function getRequiredColumns(): array
    {
        return ['code', 'name', 'country_code'];
    }

    protected function getTableTitle(): string
    {
        return 'Airports';
    }

    protected function getTableIcon(): string
    {
        return 'actions-move-up';
    }

    public function getCsvExample(): string
    {
        return 'code,name,ccd,ident,lat,lon,elevation,continent,country_code,iso_region,location_name,city_id,name_de,name_fr
BER,"Berlin Brandenburg Airport",BER,EDDB,52.351389,13.493889,157,EU,DE,DE-BB,"Berlin, Germany",1,"Flughafen Berlin Brandenburg","Aéroport de Berlin-Brandebourg"
MUC,"Munich Airport",MUC,EDDM,48.353889,11.786111,1487,EU,DE,DE-BY,"Munich, Germany",2,"Flughafen München","Aéroport de Munich"';
    }

    protected function validateHeaders(array $headers): void
    {
        $this->validateRequiredColumns($headers);
        
        // Validate airport code format in headers if needed
        // Additional validation can be added here
    }

    protected function processRow(array $rowData, array &$data, array &$translationData): void
    {
        static $rowCount = 0;
        $rowCount++;

        if ($rowCount <= 3) {
            error_log("Processing row {$rowCount}: " . json_encode(array_slice($rowData, 0, 5)));
        }

        $code = $this->sanitizeString($rowData['code']);

        if ($rowCount <= 3) {
            error_log("Sanitized code for row {$rowCount}: '{$code}'");
        }

        // Validate airport code
        if (strlen($code) !== 3) {
            throw new \Exception("Invalid airport code: {$code}. Must be exactly 3 characters.");
        }

        if (!preg_match('/^[A-Z]{3}$/', $code)) {
            throw new \Exception("Invalid airport code format: {$code}. Must be 3 uppercase letters.");
        }
        
        // Validate country code
        $countryCode = $this->sanitizeString($rowData['country_code']);
        if (strlen($countryCode) !== 2) {
            throw new \Exception("Invalid country code: {$countryCode}. Must be exactly 2 characters.");
        }
        
        $timestamp = $this->getCurrentTimestamp();
        
        // Main airport data
        $data[$code] = [
            'code' => $code,
            'ccd' => $this->sanitizeString($rowData['ccd'] ?? ''),
            'ident' => $this->sanitizeString($rowData['ident'] ?? ''),
            'lat' => $this->sanitizeString($rowData['lat'] ?? ''),
            'lon' => $this->sanitizeString($rowData['lon'] ?? ''),
            'elevation' => $this->sanitizeInt($rowData['elevation'] ?? 0),
            'continent' => $this->sanitizeString($rowData['continent'] ?? ''),
            'country_code' => $countryCode,
            'iso_region' => $this->sanitizeString($rowData['iso_region'] ?? ''),
            'location_name' => $this->sanitizeString($rowData['location_name'] ?? ''),
            'name' => $this->sanitizeString($rowData['name']),
            'city_id' => $this->sanitizeInt($rowData['city_id'] ?? 0),
            'tstamp' => $timestamp,
            'crdate' => $timestamp
        ];
        
        // Process translations
        $translationColumns = $this->extractTranslationColumns(array_keys($rowData));
        
        foreach ($translationColumns as $column => $info) {
            if ($info['base_column'] === 'name' && !$this->isEmpty($rowData[$column])) {
                $translationData[] = [
                    'code' => $code,
                    'lang' => $info['lang_code'],
                    'name' => $this->sanitizeString($rowData[$column]),
                    'tstamp' => $timestamp,
                    'crdate' => $timestamp
                ];
            }
        }
    }

    protected function validateData(array $data, array $translationData): void
    {
        error_log('=== AIRPORT DATA VALIDATION ===');
        error_log('Data array count: ' . count($data));
        error_log('Translation data count: ' . count($translationData));

        if (!empty($data)) {
            error_log('First 3 airport codes: ' . implode(', ', array_slice(array_keys($data), 0, 3)));
            $firstAirport = reset($data);
            error_log('First airport data: ' . json_encode($firstAirport));
        }

        if (empty($data)) {
            throw new \Exception('No valid airport data found in CSV');
        }

        // Check for duplicate codes
        $codes = array_keys($data);
        $duplicates = array_diff_assoc($codes, array_unique($codes));
        if (!empty($duplicates)) {
            throw new \Exception('Duplicate airport codes found: ' . implode(', ', array_unique($duplicates)));
        }

        // Validate required fields
        foreach ($data as $code => $airport) {
            if ($this->isEmpty($airport['name'])) {
                throw new \Exception("Airport {$code} is missing required name");
            }

            if ($this->isEmpty($airport['country_code'])) {
                throw new \Exception("Airport {$code} is missing required country_code");
            }
        }

        error_log('Airport data validation passed');
    }

    protected function importData(array $data, array $translationData): array
    {
        $connection = $this->connectionPool->getConnectionForTable($this->getMainTableName());

        // Start transaction
        $connection->beginTransaction();

        try {
            // Clear existing data
            $this->clearTables();

            // Insert main data
            $insertedCount = 0;
            foreach ($data as $code => $airport) {
                try {
                    $connection->insert($this->getMainTableName(), $airport);
                    $insertedCount++;
                } catch (\Exception $e) {
                    throw new \Exception("Database error inserting airport '{$code}': " . $e->getMessage() . ". Data: " . json_encode($airport));
                }
            }

            // Insert translations
            $translationCount = 0;
            foreach ($translationData as $translation) {
                try {
                    $connection->insert($this->getTranslationTableName(), $translation);
                    $translationCount++;
                } catch (\Exception $e) {
                    throw new \Exception("Database error inserting translation for '{$translation['code']}' language '{$translation['lang']}': " . $e->getMessage() . ". Data: " . json_encode($translation));
                }
            }

            // Commit transaction
            $connection->commit();

            // Calculate translation statistics by language
            $translationStats = [];
            foreach ($translationData as $translation) {
                $lang = $translation['lang'];
                if (!isset($translationStats[$lang])) {
                    $translationStats[$lang] = 0;
                }
                $translationStats[$lang]++;
            }

            return [
                'success' => true,
                'message' => "Successfully imported {$insertedCount} airports with {$translationCount} translations",
                'imported_count' => $insertedCount,
                'translation_count' => $translationCount,
                'translation_stats' => $translationStats,
                'table_type' => 'airports'
            ];

        } catch (\Exception $e) {
            // Rollback transaction
            $connection->rollBack();
            throw new \Exception('Database import failed: ' . $e->getMessage());
        }
    }
}
