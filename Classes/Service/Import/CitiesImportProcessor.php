<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Service\Import;

/**
 * Import processor for cities data
 */
class CitiesImportProcessor extends AbstractImportProcessor
{
    protected function getMainTableName(): string
    {
        return 'tx_landingpages_cities';
    }

    protected function getTranslationTableName(): string
    {
        return 'tx_landingpages_cities_i18n';
    }

    public function getRequiredColumns(): array
    {
        return ['id', 'name'];
    }

    protected function getTableTitle(): string
    {
        return 'Cities';
    }

    protected function getTableIcon(): string
    {
        return 'apps-pagetree-page-default';
    }

    public function getCsvExample(): string
    {
        return 'id,region_id,name,alt_name,ak,name_de,name_bg
1,100,Berlin,"Berlin, Germany",10,Berlin,Берлин
2,200,Munich,"München, Germany",8,München,Мюнхен
3,300,Sofia,"София, Bulgaria",15,Sofia,София';
    }

    protected function validateHeaders(array $headers): void
    {
        $this->validateRequiredColumns($headers);
    }

    protected function processRow(array $rowData, array &$data, array &$translationData): void
    {
        $id = $this->sanitizeInt($rowData['id']);
        
        // Validate city ID
        if ($id <= 0) {
            throw new \Exception("Invalid city ID: {$id}. Must be a positive integer.");
        }
        
        $timestamp = $this->getCurrentTimestamp();
        
        // Main city data
        $data[$id] = [
            'id' => $id,
            'region_id' => $this->sanitizeInt($rowData['region_id'] ?? 0),
            'name' => $this->sanitizeString($rowData['name']),
            'alt_name' => $this->sanitizeString($rowData['alt_name'] ?? ''),
            'ak' => $this->sanitizeInt($rowData['ak'] ?? 0),
            'tstamp' => $timestamp,
            'crdate' => $timestamp
        ];
        
        // Process translations
        $translationColumns = $this->extractTranslationColumns(array_keys($rowData));
        
        foreach ($translationColumns as $column => $info) {
            if ($info['base_column'] === 'name' && !$this->isEmpty($rowData[$column])) {
                $translationData[] = [
                    'id' => $id,
                    'lang' => $info['lang_code'],
                    'name' => $this->sanitizeString($rowData[$column]),
                    'tstamp' => $timestamp,
                    'crdate' => $timestamp
                ];
            }
        }
    }

    protected function validateData(array $data, array $translationData): void
    {
        if (empty($data)) {
            throw new \Exception('No valid city data found in CSV');
        }
        
        // Check for duplicate IDs
        $ids = array_keys($data);
        $duplicates = array_diff_assoc($ids, array_unique($ids));
        if (!empty($duplicates)) {
            throw new \Exception('Duplicate city IDs found: ' . implode(', ', array_unique($duplicates)));
        }
        
        // Validate required fields
        foreach ($data as $id => $city) {
            if ($this->isEmpty($city['name'])) {
                throw new \Exception("City {$id} is missing required name");
            }
        }
    }

    protected function importData(array $data, array $translationData): array
    {
        $connection = $this->connectionPool->getConnectionForTable($this->getMainTableName());
        
        // Start transaction
        $connection->beginTransaction();
        
        try {
            // Clear existing data
            $this->clearTables();
            
            // Insert main data
            $insertedCount = 0;
            foreach ($data as $city) {
                $connection->insert($this->getMainTableName(), $city);
                $insertedCount++;
            }
            
            // Insert translations
            $translationCount = 0;
            foreach ($translationData as $translation) {
                $connection->insert($this->getTranslationTableName(), $translation);
                $translationCount++;
            }
            
            // Commit transaction
            $connection->commit();
            
            return [
                'success' => true,
                'message' => "Successfully imported {$insertedCount} cities with {$translationCount} translations",
                'imported_count' => $insertedCount,
                'translation_count' => $translationCount
            ];
            
        } catch (\Exception $e) {
            // Rollback transaction
            $connection->rollBack();
            throw new \Exception('Import failed: ' . $e->getMessage());
        }
    }
}
