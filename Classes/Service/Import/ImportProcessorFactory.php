<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Service\Import;

/**
 * Factory for creating import processors
 */
class ImportProcessorFactory
{
    /**
     * Create import processor for given table type
     */
    public static function createProcessor(string $tableType): AbstractImportProcessor
    {
        switch ($tableType) {
            case 'airports':
                return new AirportsImportProcessor();
            
            case 'countries':
                return new CountriesImportProcessor();
            
            case 'cities':
                return new CitiesImportProcessor();
            
            default:
                throw new \InvalidArgumentException("Unknown table type: {$tableType}");
        }
    }

    /**
     * Get available table types
     */
    public static function getAvailableTableTypes(): array
    {
        return ['airports', 'countries', 'cities'];
    }

    /**
     * Check if table type is valid
     */
    public static function isValidTableType(string $tableType): bool
    {
        return in_array($tableType, self::getAvailableTableTypes());
    }

    /**
     * Get table configuration for all types
     */
    public static function getAllTableConfigs(): array
    {
        $configs = [];
        
        foreach (self::getAvailableTableTypes() as $tableType) {
            $processor = self::createProcessor($tableType);
            $configs[$tableType] = $processor->getTableConfig();
        }
        
        return $configs;
    }
}
