<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Service\Import;

/**
 * Import processor for countries data
 */
class CountriesImportProcessor extends AbstractImportProcessor
{
    protected function getMainTableName(): string
    {
        return 'tx_landingpages_countries';
    }

    protected function getTranslationTableName(): string
    {
        return 'tx_landingpages_countries_i18n';
    }

    public function getRequiredColumns(): array
    {
        return ['code', 'name'];
    }

    protected function getTableTitle(): string
    {
        return 'Countries';
    }

    protected function getTableIcon(): string
    {
        return 'apps-pagetree-folder-default';
    }

    public function getCsvExample(): string
    {
        return 'code,name,alt_name,phone_code,name_de,name_bg
DE,Germany,Deutschland,+49,Deutschland,Германия
BG,Bulgaria,България,+359,Bulgarien,България
FR,France,République française,+33,<PERSON><PERSON><PERSON>,Франция';
    }

    protected function validateHeaders(array $headers): void
    {
        $this->validateRequiredColumns($headers);
    }

    protected function processRow(array $rowData, array &$data, array &$translationData): void
    {
        $code = $this->sanitizeString($rowData['code']);
        
        // Validate country code
        if (strlen($code) !== 2) {
            throw new \Exception("Invalid country code: {$code}. Must be exactly 2 characters.");
        }
        
        if (!preg_match('/^[A-Z]{2}$/', $code)) {
            throw new \Exception("Invalid country code format: {$code}. Must be 2 uppercase letters.");
        }
        
        $timestamp = $this->getCurrentTimestamp();
        
        // Main country data
        $data[$code] = [
            'code' => $code,
            'name' => $this->sanitizeString($rowData['name']),
            'alt_name' => $this->sanitizeString($rowData['alt_name'] ?? ''),
            'phone_code' => $this->sanitizeString($rowData['phone_code'] ?? ''),
            'tstamp' => $timestamp,
            'crdate' => $timestamp
        ];
        
        // Process translations
        $translationColumns = $this->extractTranslationColumns(array_keys($rowData));
        
        foreach ($translationColumns as $column => $info) {
            if ($info['base_column'] === 'name' && !$this->isEmpty($rowData[$column])) {
                $translationData[] = [
                    'code' => $code,
                    'lang' => $info['lang_code'],
                    'name' => $this->sanitizeString($rowData[$column]),
                    'tstamp' => $timestamp,
                    'crdate' => $timestamp
                ];
            }
        }
    }

    protected function validateData(array $data, array $translationData): void
    {
        if (empty($data)) {
            throw new \Exception('No valid country data found in CSV');
        }
        
        // Check for duplicate codes
        $codes = array_keys($data);
        $duplicates = array_diff_assoc($codes, array_unique($codes));
        if (!empty($duplicates)) {
            throw new \Exception('Duplicate country codes found: ' . implode(', ', array_unique($duplicates)));
        }
        
        // Validate required fields
        foreach ($data as $code => $country) {
            if ($this->isEmpty($country['name'])) {
                throw new \Exception("Country {$code} is missing required name");
            }
        }
    }

    protected function importData(array $data, array $translationData): array
    {
        $connection = $this->connectionPool->getConnectionForTable($this->getMainTableName());
        
        // Start transaction
        $connection->beginTransaction();
        
        try {
            // Clear existing data
            $this->clearTables();
            
            // Insert main data
            $insertedCount = 0;
            foreach ($data as $country) {
                $connection->insert($this->getMainTableName(), $country);
                $insertedCount++;
            }
            
            // Insert translations
            $translationCount = 0;
            foreach ($translationData as $translation) {
                $connection->insert($this->getTranslationTableName(), $translation);
                $translationCount++;
            }
            
            // Commit transaction
            $connection->commit();
            
            return [
                'success' => true,
                'message' => "Successfully imported {$insertedCount} countries with {$translationCount} translations",
                'imported_count' => $insertedCount,
                'translation_count' => $translationCount
            ];
            
        } catch (\Exception $e) {
            // Rollback transaction
            $connection->rollBack();
            throw new \Exception('Import failed: ' . $e->getMessage());
        }
    }
}
