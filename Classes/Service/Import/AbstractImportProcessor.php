<?php

declare(strict_types=1);

namespace Bgs\LandingPages\Service\Import;

use Bgs\LandingPages\Exception\ImportException;
use TYPO3\CMS\Core\Database\ConnectionPool;
use TYPO3\CMS\Core\Database\Connection;
use TYPO3\CMS\Core\Utility\GeneralUtility;
use Psr\Http\Message\UploadedFileInterface;

/**
 * Abstract base class for import processors
 */
abstract class AbstractImportProcessor
{
    protected ConnectionPool $connectionPool;
    protected array $supportedLanguages = ['en', 'de', 'fr', 'es', 'it', 'bg', 'pl', 'ro', 'hu', 'cs', 'sk'];

    public function __construct()
    {
        $this->connectionPool = GeneralUtility::makeInstance(ConnectionPool::class);
    }

    /**
     * Process CSV import from uploaded file
     */
    public function processImport(UploadedFileInterface $csvFile): array
    {
        $startTime = microtime(true);
        $fileSize = $csvFile->getSize();
        $fileName = $csvFile->getClientFilename();

        // Detect file type from uploaded file
        $fileType = null;
        if ($csvFile->getClientMediaType()) {
            $fileType = $csvFile->getClientMediaType();
        } elseif ($fileName) {
            // Fallback to extension-based detection
            $extension = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            switch ($extension) {
                case 'csv':
                    $fileType = 'text/csv';
                    break;
                case 'txt':
                    $fileType = 'text/plain';
                    break;
                default:
                    $fileType = 'application/octet-stream';
            }
        }

        // Initialize detailed analysis data
        $analysisData = [
            'file_name' => $fileName,
            'file_size' => $fileSize,
            'upload_error' => $csvFile->getError(),
            'upload_error_message' => $this->getUploadErrorMessage($csvFile->getError()),
            'file_type' => $fileType,
            'detected_encoding' => null,
            'has_bom' => false,
            'total_lines' => 0,
            'header_columns' => [],
            'detected_translations' => [],
            'empty_lines_skipped' => 0,
            'data_rows_processed' => 0,
            'validation_errors' => [],
            'database_errors' => []
        ];

        // Create a wrapper to ensure analysis data is always attached to exceptions
        $attachAnalysisData = function(\Exception $e) use (&$analysisData) {
            if ($e instanceof ImportException) {
                $e->setAnalysisData($analysisData);
                return $e;
            } else {
                return new ImportException($e->getMessage(), $e->getCode(), $e, $analysisData);
            }
        };

        try {
            $csvContent = $csvFile->getStream()->getContents();

            // Detect encoding
            $detectedEncoding = mb_detect_encoding($csvContent, ['UTF-8', 'ISO-8859-1', 'Windows-1252', 'ASCII'], true);
            $analysisData['detected_encoding'] = $detectedEncoding ?: 'Unknown';

            // Check for BOM
            if (substr($csvContent, 0, 3) === "\xEF\xBB\xBF") {
                $analysisData['has_bom'] = true;
                $csvContent = substr($csvContent, 3);
                // If BOM detected, it's definitely UTF-8
                $analysisData['detected_encoding'] = 'UTF-8';
            }

            // Detect and normalize line endings
            $originalLength = strlen($csvContent);
            $crlfCount = substr_count($csvContent, "\r\n");
            $crCount = substr_count($csvContent, "\r") - $crlfCount; // Subtract CRLF to get pure CR count
            $lfCount = substr_count($csvContent, "\n") - $crlfCount; // Subtract CRLF to get pure LF count

            error_log("Line ending detection: CRLF={$crlfCount}, CR={$crCount}, LF={$lfCount}");
            error_log("First 200 chars of CSV: " . substr($csvContent, 0, 200));
            error_log("Last 200 chars of CSV: " . substr($csvContent, -200));

            // If no line endings detected, this might be a malformed CSV
            if ($crlfCount === 0 && $crCount === 0 && $lfCount === 0) {
                error_log("WARNING: No line endings detected in CSV file. File might be malformed or single-line.");
                // Try to detect if this is actually a single-line CSV with many columns
                $firstLineTest = str_getcsv($csvContent);
                error_log("Single-line CSV test - detected columns: " . count($firstLineTest));
                if (count($firstLineTest) > 50) {
                    throw new ImportException("CSV file appears to be malformed - detected as single line with " . count($firstLineTest) . " columns. Please check your CSV file format and ensure it has proper line breaks between rows.");
                }
            }

            // Normalize line endings to handle \r\n, \r, and \n
            $csvContent = str_replace(["\r\n", "\r"], "\n", $csvContent);
            $analysisData['line_endings'] = [
                'crlf' => $crlfCount,
                'cr' => $crCount,
                'lf' => $lfCount,
                'normalized' => true
            ];

            // Parse CSV
            $lines = str_getcsv($csvContent, "\n");
            $analysisData['total_lines'] = count($lines);

            error_log('=== CSV PARSING DEBUG ===');
            error_log('Total lines in CSV: ' . count($lines));
            error_log('First 3 lines: ' . json_encode(array_slice($lines, 0, 3)));

            if (empty($lines)) {
                throw new \Exception('CSV file is empty - no data found');
            }

            // Get header row
            $header = str_getcsv(array_shift($lines));
            $analysisData['header_columns'] = $header;
            $analysisData['detected_translations'] = $this->extractTranslationColumns($header);

            error_log('Header columns: ' . json_encode($header));
            error_log('Required columns: ' . json_encode($this->getRequiredColumns()));

            try {
                $this->validateHeaders($header);
            } catch (\Exception $e) {
                throw $attachAnalysisData($e);
            }

            // Parse data rows
            $data = [];
            $translationData = [];
            $emptyLinesSkipped = 0;

            error_log('Starting to process ' . count($lines) . ' data rows');

            foreach ($lines as $lineNumber => $line) {
                if (trim($line) === '') {
                    $emptyLinesSkipped++;
                    continue; // Skip empty lines
                }

                $row = str_getcsv($line);
                if (count($row) !== count($header)) {
                    error_log("Column count mismatch on line " . ($lineNumber + 2) . ": got " . count($row) . ", expected " . count($header));
                    throw new \Exception("Row " . ($lineNumber + 2) . " has " . count($row) . " columns but expected " . count($header) . " columns. Data: " . substr($line, 0, 100) . "...");
                }

                $rowData = array_combine($header, $row);

                try {
                    $this->processRow($rowData, $data, $translationData);
                    $analysisData['data_rows_processed']++;
                } catch (\Exception $e) {
                    error_log("Error processing row " . ($lineNumber + 2) . ": " . $e->getMessage());
                    $rowException = new \Exception("Error processing row " . ($lineNumber + 2) . ": " . $e->getMessage() . ". Row data: " . json_encode(array_slice($rowData, 0, 5)) . "...");
                    throw $attachAnalysisData($rowException);
                }
            }

            $analysisData['empty_lines_skipped'] = $emptyLinesSkipped;
            error_log('Finished processing rows. Data count: ' . count($data) . ', Translation count: ' . count($translationData));

            // Validate data
            try {
                $this->validateData($data, $translationData);
            } catch (\Exception $e) {
                throw $attachAnalysisData($e);
            }

            // Import data with transaction
            $result = $this->importData($data, $translationData);

            // Add timing and analysis information
            $endTime = microtime(true);
            $result['processing_time'] = round($endTime - $startTime, 2);
            $result['analysis'] = $analysisData;

            return $result;

        } catch (\Exception $e) {
            // Add analysis data to exception for better error reporting
            $analysisData['error_message'] = $e->getMessage();
            $analysisData['processing_time'] = round(microtime(true) - $startTime, 2);

            // Create a custom exception that carries the analysis data
            throw new ImportException($e->getMessage(), $e->getCode(), $e, $analysisData);
        }
    }

    /**
     * Validate CSV headers
     */
    abstract protected function validateHeaders(array $headers): void;

    /**
     * Process a single CSV row
     */
    abstract protected function processRow(array $rowData, array &$data, array &$translationData): void;

    /**
     * Validate parsed data before import
     */
    abstract protected function validateData(array $data, array $translationData): void;

    /**
     * Import data to database
     */
    abstract protected function importData(array $data, array $translationData): array;

    /**
     * Get main table name
     */
    abstract protected function getMainTableName(): string;

    /**
     * Get translation table name
     */
    abstract protected function getTranslationTableName(): string;

    /**
     * Get required columns for this table type
     */
    abstract public function getRequiredColumns(): array;

    /**
     * Get CSV example for this table type
     */
    abstract public function getCsvExample(): string;

    /**
     * Clear existing data from tables
     */
    protected function clearTables(): void
    {
        $connection = $this->connectionPool->getConnectionForTable($this->getMainTableName());
        
        // Disable foreign key checks temporarily
        $connection->executeStatement('SET FOREIGN_KEY_CHECKS = 0');
        
        try {
            // Clear translation table first
            $connection->truncate($this->getTranslationTableName());
            
            // Clear main table
            $connection->truncate($this->getMainTableName());
        } finally {
            // Re-enable foreign key checks
            $connection->executeStatement('SET FOREIGN_KEY_CHECKS = 1');
        }
    }

    /**
     * Extract translation columns from headers
     */
    protected function extractTranslationColumns(array $headers): array
    {
        $translationColumns = [];
        
        foreach ($headers as $header) {
            // Check if header matches pattern: column_langcode
            if (preg_match('/^(.+)_([a-z]{2})$/', $header, $matches)) {
                $baseColumn = $matches[1];
                $langCode = $matches[2];
                
                if (in_array($langCode, $this->supportedLanguages)) {
                    $translationColumns[$header] = [
                        'base_column' => $baseColumn,
                        'lang_code' => $langCode
                    ];
                }
            }
        }
        
        return $translationColumns;
    }

    /**
     * Get current timestamp
     */
    protected function getCurrentTimestamp(): int
    {
        return time();
    }

    /**
     * Validate that required columns exist
     */
    protected function validateRequiredColumns(array $headers): void
    {
        $requiredColumns = $this->getRequiredColumns();
        $missingColumns = array_diff($requiredColumns, $headers);
        
        if (!empty($missingColumns)) {
            throw new \Exception('Missing required columns: ' . implode(', ', $missingColumns));
        }
    }

    /**
     * Check if value is empty (null, empty string, or whitespace only)
     */
    protected function isEmpty($value): bool
    {
        return $value === null || $value === '' || trim($value) === '';
    }

    /**
     * Sanitize string value
     */
    protected function sanitizeString(?string $value): string
    {
        if ($value === null) {
            return '';
        }
        
        return trim($value);
    }

    /**
     * Sanitize integer value
     */
    protected function sanitizeInt($value): int
    {
        if ($this->isEmpty($value)) {
            return 0;
        }
        
        return (int) $value;
    }

    /**
     * Get table configuration for UI
     */
    public function getTableConfig(): array
    {
        return [
            'title' => $this->getTableTitle(),
            'icon' => $this->getTableIcon(),
            'requiredColumns' => implode(', ', $this->getRequiredColumns()),
            'csvExample' => $this->getCsvExample()
        ];
    }

    /**
     * Get table title for UI
     */
    abstract protected function getTableTitle(): string;

    /**
     * Get table icon for UI
     */
    abstract protected function getTableIcon(): string;

    /**
     * Get human-readable upload error message
     */
    protected function getUploadErrorMessage(int $errorCode): string
    {
        switch ($errorCode) {
            case UPLOAD_ERR_OK:
                return 'No error';
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize directive';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE directive';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'File upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }
}
