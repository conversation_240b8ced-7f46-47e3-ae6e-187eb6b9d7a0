# Reference Data Tables Implementation

## Overview

This document describes the implementation of reference data tables for the Flight Landing Pages extension. These tables store airport, country, and city data that can be used programmatically throughout the extension for flight route management and content generation.

**Important**: These tables are NOT integrated with TYPO3's backend interface. They are pure database tables designed for programmatic access only.

## Implemented Tables

### 1. Airports (`tx_landingpages_airports`)

**Purpose**: Store airport reference data including codes, names, and geographic information.

**Key Fields**:
- `code` (varchar(3)) - Primary key, IATA airport code (e.g., "BER")
- `name` (varchar(250)) - Full airport name (e.g., "Berlin Brandenburg Airport")
- `ident` (varchar(10)) - ICAO identifier (e.g., "EDDB")
- `lat`, `lon` (varchar(20)) - Geographic coordinates
- `country_code` (varchar(2)) - ISO country code
- `continent` (varchar(3)) - Continent code
- `city_id` (int(11)) - Reference to cities table

### 2. Airport Translations (`tx_landingpages_airports_i18n`)

**Purpose**: Store translated airport names for multilingual support.

**Key Fields**:
- `code` (varchar(3)) - Reference to airport code
- `lang` (varchar(5)) - Language code (e.g., "en", "de", "fr")
- `name` (varchar(250)) - Translated airport name

### 3. Countries (`tx_landingpages_countries`)

**Purpose**: Store country reference data.

**Key Fields**:
- `code` (varchar(2)) - Primary key, ISO country code (e.g., "DE")
- `name` (varchar(200)) - Country name (e.g., "Germany")
- `alt_name` (varchar(255)) - Alternative name (e.g., "Deutschland")
- `phone_code` (varchar(6)) - International phone code (e.g., "+49")

### 4. Country Translations (`tx_landingpages_countries_i18n`)

**Purpose**: Store translated country names for multilingual support.

**Key Fields**:
- `code` (varchar(2)) - Reference to country code
- `lang` (varchar(2)) - Language code
- `name` (varchar(200)) - Translated country name

### 5. Cities (`tx_landingpages_cities`)

**Purpose**: Store city reference data.

**Key Fields**:
- `id` (int(11)) - Primary key, unique city identifier
- `region_id` (int(11)) - Regional identifier
- `name` (varchar(250)) - City name (e.g., "Berlin")
- `alt_name` (varchar(255)) - Alternative name
- `ak` (int(11)) - AK sorting priority (0 = disabled, higher = more important)

### 6. City Translations (`tx_landingpages_cities_i18n`)

**Purpose**: Store translated city names for multilingual support.

**Key Fields**:
- `id` (int(11)) - Reference to city ID
- `lang` (varchar(2)) - Language code
- `name` (varchar(250)) - Translated city name

## Technical Implementation

### Database Schema

All tables follow TYPO3 v12.04 conventions:
- Table names prefixed with `tx_landingpages_`
- Standard TYPO3 fields: `tstamp`, `crdate`
- Proper indexing for performance
- No foreign key constraints (TYPO3 best practice)

### Database Access Only

These tables are designed for programmatic access only:
- No TCA configuration (not accessible via TYPO3 backend)
- No backend forms or editing interfaces
- No TYPO3 List module integration
- Pure database tables for API/service layer usage

## Usage

These reference data tables can be used programmatically for:

1. **Flight Route Validation**: Validate origin/destination codes against airport data via database queries
2. **Content Generation**: Generate dynamic content using real airport/city/country names through repository classes
3. **Multilingual Support**: Display localized names based on site language using translation tables
4. **Data Import**: Import flight data from external APIs using standardized codes
5. **Search and Filtering**: Enable advanced search by location, country, or region through custom services
6. **API Integration**: Serve as reference data for REST APIs or GraphQL endpoints

## Next Steps

1. **Repository Classes**: Create Extbase repository classes for programmatic access
2. **Data Import Services**: Create import functionality for populating tables with real data
3. **API Integration**: Connect to external flight data APIs using these reference codes
4. **Validation Services**: Add validation services for flight routes using this reference data
5. **Search Services**: Implement search and autocomplete services using this data
6. **Command Line Tools**: Create CLI commands for data management and import

## Files Modified/Created

### Database Schema
- `ext_tables.sql` - Added 6 new table definitions

### Documentation
- `REFERENCE_DATA_TABLES_IMPLEMENTATION.md` - This documentation file

The implementation follows TYPO3 v12.04 best practices and provides a solid foundation for programmatic access to flight-related reference data within the extension.

## Database Access Examples

### Direct Database Queries
```php
// Get airport by code
$queryBuilder = GeneralUtility::makeInstance(ConnectionPool::class)
    ->getQueryBuilderForTable('tx_landingpages_airports');
$airport = $queryBuilder
    ->select('*')
    ->from('tx_landingpages_airports')
    ->where($queryBuilder->expr()->eq('code', $queryBuilder->createNamedParameter('BER')))
    ->executeQuery()
    ->fetchAssociative();
```

### Repository Pattern (Recommended)
```php
// Create repository classes in Classes/Domain/Repository/
// AirportRepository, CountryRepository, CityRepository
// Access via dependency injection in your services
```
