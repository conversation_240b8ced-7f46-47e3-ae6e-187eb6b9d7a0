<?php

/**
 * Backend module configuration for Landing Pages extension
 */
return [
    'web_landingpages' => [
        'parent' => 'web',
        'position' => ['after' => 'web_info'],
        'access' => 'user',
        'workspaces' => 'live',
        'path' => '/module/web/landingpages',
        'labels' => 'LLL:EXT:landing-pages/Resources/Private/Language/locallang_mod.xlf',
        'iconIdentifier' => 'module-landing-pages',
        'routes' => [
            '_default' => [
                'target' => \Bgs\LandingPages\Controller\Backend\LandingPagesModuleController::class . '::indexAction',
            ],
            'dataOverview' => [
                'target' => \Bgs\LandingPages\Controller\Backend\LandingPagesModuleController::class . '::dataOverviewAction',
            ],
            'importForm' => [
                'target' => \Bgs\LandingPages\Controller\Backend\LandingPagesModuleController::class . '::importFormAction',
            ],
            'import' => [
                'target' => \Bgs\LandingPages\Controller\Backend\LandingPagesModuleController::class . '::importAction',
            ],
        ],
    ],
];
