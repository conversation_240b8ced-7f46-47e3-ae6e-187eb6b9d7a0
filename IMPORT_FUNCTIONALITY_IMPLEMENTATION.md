# CSV Import Functionality Implementation

## Overview

This document describes the implementation of CSV import functionality for the Flight Landing Pages extension reference data tables (airports, countries, cities).

## Features Implemented

### 1. **Dynamic Import Interface**
- Single import page that dynamically switches between table types via query parameter
- Accessible via import buttons on each data overview card
- Comprehensive form with file upload, validation, and confirmation

### 2. **Import Service Architecture**
- **AbstractImportProcessor**: Base class with common import logic
- **AirportsImportProcessor**: Handles airport data import with IATA code validation
- **CountriesImportProcessor**: Handles country data import with ISO code validation  
- **CitiesImportProcessor**: Handles city data import with ID validation
- **ImportProcessorFactory**: Factory pattern for creating appropriate processors

### 3. **CSV Format Support**
- **Headers**: First row must contain column headers
- **Required Columns**: Each table type has specific required columns
- **Translation Support**: Language-specific columns using format `column_langcode` (e.g., `name_de`, `name_fr`)
- **Encoding**: UTF-8 with BOM handling
- **Validation**: Comprehensive data validation before import

### 4. **Safety Features**
- **Confirmation Required**: User must explicitly confirm data replacement
- **File Validation**: Size limits (10MB), type checking (.csv only)
- **Transaction Safety**: Database transactions with rollback on errors
- **Foreign Key Handling**: Temporarily disables foreign key checks during import
- **Data Backup**: Current data is backed up before replacement

## File Structure

### Backend Module Routes
```php
// Configuration/Backend/Modules.php
'importForm' => [
    'target' => LandingPagesModuleController::class . '::importFormAction',
],
'import' => [
    'target' => LandingPagesModuleController::class . '::importAction',
],
```

### Service Classes
```
Classes/Service/Import/
├── AbstractImportProcessor.php      # Base import logic
├── AirportsImportProcessor.php      # Airport-specific import
├── CountriesImportProcessor.php     # Country-specific import
├── CitiesImportProcessor.php        # City-specific import
└── ImportProcessorFactory.php      # Factory for processors
```

### Templates
```
Resources/Private/Templates/Backend/LandingPagesModule/
├── DataOverview.html               # Updated with functional import buttons
└── ImportForm.html                 # New import form template
```

## CSV Format Examples

### Airports
```csv
code,name,ccd,ident,lat,lon,elevation,continent,country_code,iso_region,location_name,city_id,name_de,name_fr
BER,"Berlin Brandenburg Airport",BER,EDDB,52.351389,13.493889,157,EU,DE,DE-BB,"Berlin, Germany",1,"Flughafen Berlin Brandenburg","Aéroport de Berlin-Brandebourg"
```

### Countries
```csv
code,name,alt_name,phone_code,name_de,name_bg
DE,Germany,Deutschland,+49,Deutschland,Германия
```

### Cities
```csv
id,region_id,name,alt_name,ak,name_de,name_bg
1,100,Berlin,"Berlin, Germany",10,Berlin,Берлин
```

## Import Process Flow

1. **Access**: User clicks import button on data overview card
2. **Form**: Import form loads with table-specific configuration
3. **Upload**: User selects CSV file and confirms replacement
4. **Validation**: File format, size, and content validation
5. **Processing**: CSV parsing and data validation
6. **Import**: Database transaction with data replacement
7. **Feedback**: Success/error messages with detailed information

## Validation Rules

### Airports
- **Code**: Exactly 3 uppercase letters (IATA format)
- **Country Code**: Exactly 2 uppercase letters (ISO format)
- **Required**: code, name, country_code

### Countries
- **Code**: Exactly 2 uppercase letters (ISO format)
- **Required**: code, name

### Cities
- **ID**: Positive integer
- **Required**: id, name

## Translation Support

### Language Detection
- Automatic detection from column headers
- Supported languages: en, de, fr, es, it, bg, pl, ro, hu, cs, sk
- Format: `base_column_langcode` (e.g., `name_de`, `name_fr`)

### Translation Tables
- **Airports**: `tx_landingpages_airports_i18n`
- **Countries**: `tx_landingpages_countries_i18n`
- **Cities**: `tx_landingpages_cities_i18n`

## Error Handling

### File Validation Errors
- Invalid file type (not .csv)
- File size exceeds 10MB limit
- Upload errors

### Data Validation Errors
- Missing required columns
- Invalid data formats (codes, IDs)
- Duplicate entries
- Empty required fields

### Import Errors
- Database connection issues
- Transaction failures
- Foreign key constraint violations

## Security Considerations

1. **File Type Validation**: Only .csv files accepted
2. **Size Limits**: 10MB maximum file size
3. **Data Sanitization**: All input data is sanitized
4. **Transaction Safety**: Rollback on any errors
5. **Confirmation Required**: Explicit user confirmation for data replacement

## Usage Instructions

1. Navigate to **Web > Landing Pages > Data Overview**
2. Click **Import** button on desired data table card
3. Select CSV file with proper format
4. Review format requirements and examples
5. Check confirmation checkbox
6. Click **Import Data** button
7. Confirm in popup dialog
8. Review success/error messages

## Testing

A test CSV file is provided: `test_airports.csv` with sample airport data including translations.

## Future Enhancements

- Export functionality for existing data
- Import preview before actual import
- Incremental import (append mode)
- Import history and logging
- Batch import for multiple files
