<?php

/**
 * Simple test script to verify import functionality
 * Run with: ddev php test_import_functionality.php
 */

require_once __DIR__ . '/../../vendor/autoload.php';

use Bgs\LandingPages\Service\Import\ImportProcessorFactory;

echo "Testing Import Functionality\n";
echo "============================\n\n";

// Test factory
echo "1. Testing ImportProcessorFactory...\n";
$availableTypes = ImportProcessorFactory::getAvailableTableTypes();
echo "Available table types: " . implode(', ', $availableTypes) . "\n";

foreach ($availableTypes as $type) {
    echo "Testing {$type} processor...\n";
    
    try {
        $processor = ImportProcessorFactory::createProcessor($type);
        $config = $processor->getTableConfig();
        
        echo "  - Title: {$config['title']}\n";
        echo "  - Icon: {$config['icon']}\n";
        echo "  - Required columns: {$config['requiredColumns']}\n";
        echo "  - CSV example preview: " . substr($config['csvExample'], 0, 50) . "...\n";
        echo "  ✓ {$type} processor created successfully\n\n";
        
    } catch (Exception $e) {
        echo "  ✗ Error creating {$type} processor: " . $e->getMessage() . "\n\n";
    }
}

// Test invalid type
echo "2. Testing invalid table type...\n";
try {
    ImportProcessorFactory::createProcessor('invalid');
    echo "  ✗ Should have thrown exception for invalid type\n";
} catch (Exception $e) {
    echo "  ✓ Correctly threw exception: " . $e->getMessage() . "\n";
}

echo "\n3. Testing validation methods...\n";
echo "Valid types: " . (ImportProcessorFactory::isValidTableType('airports') ? 'airports ✓' : 'airports ✗') . "\n";
echo "Invalid types: " . (ImportProcessorFactory::isValidTableType('invalid') ? 'invalid ✓' : 'invalid ✗') . "\n";

echo "\nImport functionality test completed!\n";
